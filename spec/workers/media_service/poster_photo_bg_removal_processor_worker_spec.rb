require 'rails_helper'

RSpec.describe MediaService::PosterPhotoBgRemovalProcessorWorker, type: :worker do
  let(:user) { create(:user) }
  let(:photo) { create(:photo, user: user) }
  let(:valid_key) { 'test_key' }
  let(:worker) { described_class.new }

  before do
    stub_const("User::BG_REMOVAL_POSTER_PHOTO_KEYS", [valid_key])
  end

  describe '#perform' do
    context 'with invalid inputs' do
      it 'returns early when key is blank' do
        expect(worker.perform(photo.id, nil, user.id)).to be_nil
        expect(worker.perform(photo.id, '', user.id)).to be_nil
      end

      it 'returns early when key is not in allowed keys' do
        expect(worker.perform(photo.id, 'invalid_key', user.id)).to be_nil
      end

      it 'raises PhotoNotFoundError when photo does not exist' do
        # Make sure the Photo.find is actually being called
        allow(Photo).to receive(:find).with(999).and_raise(ActiveRecord::RecordNotFound)

        expect {
          worker.perform(999, valid_key, user.id)
        }.to raise_error(PhotoNotFoundError)
      end
    end

    context 'with valid inputs' do
      let(:mock_response) { instance_double(Net::HTTPResponse) }
      let(:processed_photo) { build(:photo, user: user) }

      before do
        allow(JsonWebToken).to receive(:encode).and_return('dummy_token')

        # Mock HTTP Response
        allow(mock_response).to receive(:code).and_return('201')
        allow(mock_response).to receive(:body).and_return({
                                                            service: 'background_removal',
                                                            processed_url: 'https://example.com/processed.jpg'
                                                          }.to_json)

        # Mock Net::HTTP
        allow_any_instance_of(Net::HTTP).to receive(:request).and_return(mock_response)

        # Mock Photo creation
        allow(Photo).to receive(:new).and_return(processed_photo)
        allow(processed_photo).to receive(:save!).and_return(true)

        # Mock Photo lookup
        allow(Photo).to receive(:find_by).with(id: photo.id).and_return(photo)

        # Mock file operations
        allow(URI).to receive(:open).with(anything).and_return(
          double('PhotoFile',
                 respond_to?: true,
                 close: true,
                 path: 'test.jpg'
          )
        )
      end

      it 'processes the photo successfully' do
        # Instead of checking the return value, verify that the photo was processed
        # by checking that the necessary methods were called
        expect(processed_photo).to receive(:save!).and_return(true)

        worker.perform(photo.id, valid_key, user.id)
      end

      it 'creates or updates user metadata' do
        user_metadata = build(:user_metadatum, user: user, key: valid_key)
        allow(UserMetadatum).to receive(:find_or_create_by)
                                  .with(user_id: user.id, key: valid_key)
                                  .and_return(user_metadata)

        # Set up expectation before calling the method
        expect(user_metadata).to receive(:update)
                                   .with(value: processed_photo.id)
                                   .once

        worker.perform(photo.id, valid_key, user.id)
      end
    end

    context 'when background removal fails' do
      before do
        allow(JsonWebToken).to receive(:encode).and_return('dummy_token')

        # Mock Photo lookup
        allow(Photo).to receive(:find_by).with(id: photo.id).and_return(photo)

        stub_request(:post, Constants.get_photo_bg_removal_url_for_internal_services)
          .to_return(
            status: 422,
            body: { error: 'Processing failed' }.to_json,
            headers: { 'Content-Type' => 'application/json' }
          )

        allow(URI).to receive(:open).with(anything).and_return(
          double('PhotoFile',
                 respond_to?: true,
                 close: true,
                 path: 'test.jpg'
          )
        )
      end

      it 'raises BackgroundRemovalFailed error' do
        expect {
          worker.perform(photo.id, valid_key, user.id)
        }.to raise_error(BackgroundRemovalFailed)
      end
    end

    context 'when network error occurs' do
      before do
        allow(JsonWebToken).to receive(:encode).and_return('dummy_token')

        allow(URI).to receive(:open).with(photo.compressed_url(size: 1440, quality: 100)).and_return(
          double('PhotoFile',
                 respond_to?: true,
                 close: true,
                 path: 'test.jpg'
          )
        )

        stub_request(:post, Constants.get_photo_bg_removal_url_for_internal_services)
          .with(
            headers: { 'Authorization' => 'Bearer dummy_token' }
          )
          .to_raise(Errno::ECONNREFUSED.new)
      end
    end

    context 'content type detection' do
      it 'detects jpeg content type' do
        allow(photo).to receive(:url).and_return('image.jpg')
        expect(worker.send(:detect_content_type, photo)).to eq('image/jpeg')

        allow(photo).to receive(:url).and_return('image.jpeg')
        expect(worker.send(:detect_content_type, photo)).to eq('image/jpeg')
      end

      it 'detects png content type' do
        allow(photo).to receive(:url).and_return('image.png')
        expect(worker.send(:detect_content_type, photo)).to eq('image/png')
      end

      it 'detects gif content type' do
        allow(photo).to receive(:url).and_return('image.gif')
        expect(worker.send(:detect_content_type, photo)).to eq('image/gif')
      end

      it 'returns nil for unknown extensions' do
        allow(photo).to receive(:url).and_return('image.unknown')
        expect(worker.send(:detect_content_type, photo)).to be_nil
      end
    end
  end
end
