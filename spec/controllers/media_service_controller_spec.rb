require 'rails_helper'

RSpec.describe MediaServiceController, type: :controller do
  describe "GET /index" do
    context "check if the request is coming from valid service" do
      it "returns http unauthorized" do
        @token = JsonWebToken.encode({ service: 'dummy' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        get "index"
        expect(response).to have_http_status(:unauthorized)
      end

      it "w/ invalid token, returns http unauthorized" do
        @request.headers['Authorization'] = "Bearer alskjdfhlsakdfhlkdsjfh"
        get "index"
        expect(response).to have_http_status(:unauthorized)
      end

      it "w/ other service valid token, returns unauthorized" do
        @token = JsonWebToken.encode({ service: 'dm' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        get :index
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  describe "GET /index" do
    context "request from media service" do
      it "w/ valid token, returns http success" do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        get "index"
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe "POST update-video-process" do
    context "request from media service" do
      before :each do
        video =
          {
            "file_id": "3bb2ae5b0476337bc6f2852f5825cbbf",
            "file_name": "3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "original_file_name": "sample_video.mp4",
            "path": "50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.mp4",
            "thumbnail": {
              "path": "3bb2ae5b0476337bc6f2852f5825cbbf.jpg",
              "cdn_url": "https://gruv-cdn.circleapp.in/50/3bb2ae5b0476337bc6f2852f5825cbbf.jpg"
            }
          }
        @post = FactoryBot.create(:post)
        @video = FactoryBot.create(:video, ms_data: video, service: :gcp)
        @post_video = FactoryBot.create(:post_video, post: @post, video: @video)

        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end
      it "update the video status to processed" do
        post :update_video_process, params: { processed_url: "processed_url", metadata: {video_id: @video.id} }

        expect(response).to have_http_status(:success)

        @video.reload
        expect(@video.status).to eq("processed")
      end

      it "return unprocessable entity if params not present" do
        post :update_video_process

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "return unprocessable entity if metadata not present" do
        post :update_video_process, params: { processed_url: "", metadata: { } }

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "return unprocessable entity if processed_url not present" do
        post :update_video_process, params: { processed_url: "", metadata: { video_id: @video.id } }

        expect(response).to have_http_status(:unprocessable_entity)
      end

      it "return unprocessable entity if video_id not present" do
        post :update_video_process,params: { processed_url: "", metadata: { video_id: nil } }

        expect(response).to have_http_status(:unprocessable_entity)
      end
    end
  end

  describe "POST my_poster_page" do
    context "when user_id is not present" do
      before :each do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end
      it "should return 'User not found' message" do
        post :my_poster_page
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("User not found")
      end
    end
    context "when creative_id is not present" do
      before :each do
        @user = FactoryBot.create(:user)
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end
      it "should return 'Creative not found' message" do
        post :my_poster_page, params: { user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("Creative not found")
      end

      it "should return Layout not found message" do
        post :my_poster_page, params: { user_id: @user.id }
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
        post :my_poster_page, params: { user_id: @user.id, creative_id: @poster_creative.id, frame_id: 102 }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("Layout not found")
      end
    end

    context "when creative_id is present and frame_id is present" do
      before :each do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
        @user = FactoryBot.create(:user, name: "testing")
        photo = fixture_file_upload("app/assets/images/praja-full-logo.png", "image/png")
        @user.poster_photo = FactoryBot.create(:admin_medium, blob_data: photo)
        @user.poster_photo_with_background = FactoryBot.create(:admin_medium, blob_data: photo)
        AppVersionSupport.new('2306.27.01')

        @user_poster_layout =
          FactoryBot.create(:user_poster_layout, entity: @user,
                            h1_count: 1,
                            h2_count: 1,
                            user_leader_photos: [
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_1, priority: 1),
                              FactoryBot.create(:user_leader_photo, photo: FactoryBot.create(:admin_medium, blob_data: photo),
                                                header_type: :header_2, priority: 1)])
        @frame = FactoryBot.create(:frame)
        @user_frame = FactoryBot.create(:user_frame, user: @user, frame: @frame)
        image_630x940 = fixture_file_upload("app/assets/images/poster_creative_630x940.png", "image/png")
        image_600x750 = fixture_file_upload("app/assets/images/poster_creative_600x750.png", "image/png")
        @admin_medium_1 = FactoryBot.create(:admin_medium, blob_data: image_600x750)
        @admin_medium_2 = FactoryBot.create(:admin_medium, blob_data: image_630x940)
        @poster_creative = FactoryBot.create(:poster_creative, event: @event, photo_v3: @admin_medium_1,
                                             photo_v2: @admin_medium_2, primary: true)
      end
      it "should return layout and creative" do
        post :my_poster_page, params: { creative_id: @poster_creative.id, frame_id: @frame.id , user_id: @user.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["layout"]).not_to be_nil
        expect(response_body["creative"]).not_to be_nil
      end

      it "should return user not found message" do
        post :my_poster_page, params: { creative_id: @poster_creative.id, frame_id: @frame.id , user_id: 0 }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("User not found")
      end
    end
  end

  describe "POST protocol_page" do
    context "when user_id is not present" do
      before :each do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end

      it "should return 'User not found' message" do
        post :protocol_page
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("User not found")
      end
    end

    context "when user_id is present but user does not exist" do
      before :each do
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end

      it "should return 'User not found' message" do
        post :protocol_page, params: { user_id: 99999 }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["message"]).to eq("User not found")
      end
    end

    context "when user exists but has no layout" do
      before :each do
        @user = FactoryBot.create(:user)
        # Mock the get_layout_and_header_photos to return layout_0_0 when no layout exists
        allow_any_instance_of(User).to receive(:get_layout_and_header_photos).and_return(['layout_0_0', [], []])
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end

      it "should return error when user has no poster layout" do
        expect(Honeybadger).not_to receive(:notify).with('Protocol page: No protocol photos found for user')

        post :protocol_page, params: { user_id: @user.id }
        expect(response).to have_http_status(:not_found)
        response_body = JSON.parse(response.body)
        expect(response_body["error"]).to eq("User poster layout not found")
      end
    end

    context "when user has layout but no photos" do
      before :each do
        @user = FactoryBot.create(:user)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user, active: true, h1_count: 2, h2_count: 3)
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end

      it "should return empty protocol_photos array and notify Honeybadger" do
        expect(Honeybadger).to receive(:notify).with('Protocol page: No protocol photos found for user')

        post :protocol_page, params: { user_id: @user.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to eq(true)
        expect(response_body["protocol_photos"]).to eq([])
      end
    end

    context "when user has layout_0_0 but no photos" do
      before :each do
        @user = FactoryBot.create(:user)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user, active: true, h1_count: 0, h2_count: 0)
        # Mock the get_layout_and_header_photos to return layout_0_0
        allow_any_instance_of(User).to receive(:get_layout_and_header_photos).and_return(['layout_0_0', [], []])
        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end

      it "should return empty protocol_photos array without Honeybadger notification" do
        expect(Honeybadger).not_to receive(:notify).with('Protocol page: No protocol photos found for user')

        post :protocol_page, params: { user_id: @user.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)
        expect(response_body["success"]).to eq(true)
        expect(response_body["protocol_photos"]).to eq([])
      end
    end

    context "when user has layout with photos" do
      before :each do
        @user = FactoryBot.create(:user)
        @user_poster_layout = FactoryBot.create(:user_poster_layout, entity: @user, active: true, h1_count: 2, h2_count: 3)

        # Create some test photos
        @photo1 = FactoryBot.create(:photo)
        @photo2 = FactoryBot.create(:photo)
        @photo3 = FactoryBot.create(:photo)

        # Create user leader photos for header_1
        FactoryBot.create(:user_leader_photo,
                         user_poster_layout: @user_poster_layout,
                         photo: @photo1,
                         header_type: 'header_1',
                         priority: 1)
        FactoryBot.create(:user_leader_photo,
                         user_poster_layout: @user_poster_layout,
                         photo: @photo2,
                         header_type: 'header_1',
                         priority: 2)

        # Create user leader photos for header_2
        FactoryBot.create(:user_leader_photo,
                         user_poster_layout: @user_poster_layout,
                         photo: @photo3,
                         header_type: 'header_2',
                         priority: 1)

        @token = JsonWebToken.encode({ service: 'media' })
        @request.headers['Authorization'] = "Bearer #{@token}"
      end

      it "should return protocol_photos with combined header_1 and header_2 photos" do
        post :protocol_page, params: { user_id: @user.id }
        expect(response).to have_http_status(:success)
        response_body = JSON.parse(response.body)

        expect(response_body["success"]).to eq(true)
        expect(response_body["protocol_photos"]).to be_an(Array)
        expect(response_body["protocol_photos"].length).to eq(3) # 2 header_1 + 1 header_2

        # Check that each photo has the required structure
        response_body["protocol_photos"].each do |photo|
          expect(photo).to have_key("radius")
          expect(photo).to have_key("position_x")
          expect(photo).to have_key("position_y")
          expect(photo).to have_key("photo_url")
          expect(photo["radius"]).to be_a(Numeric)
          expect(photo["position_x"]).to be_a(Numeric)
          expect(photo["position_y"]).to be_a(Numeric)
          expect(photo["photo_url"]).to be_a(String)
        end
      end
    end

    context "when service token is invalid" do
      it "should return unauthorized" do
        @token = JsonWebToken.encode({ service: 'invalid' })
        @request.headers['Authorization'] = "Bearer #{@token}"

        post :protocol_page, params: { user_id: 1 }
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
