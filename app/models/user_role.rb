class UserRole < ApplicationRecord
  include AASM
  has_paper_trail skip: [:is_celebrated]
  attr_accessor :marketing_consent
  enum grade_level: Role::GRADE_LEVEL
  enum badge_color: Role::BADGE_COLOR

  # TODO :: Need to remove this once roles restructuring is done
  has_one :primary_circle, primary_key: :primary_circle_id, foreign_key: :id, class_name: 'Circle'
  has_one :parent_circle, primary_key: :parent_circle_id, foreign_key: :id, class_name: 'Circle'
  has_one :purview_circle, primary_key: :purview_circle_id, foreign_key: :id, class_name: 'Circle'
  has_one_attached :file
  belongs_to :role
  belongs_to :user
  belongs_to :badge_icon, optional: true

  DEFAULT_USER_ROLE_END_DATE = Date.parse("9999-12-31")

  validates :grade_level, inclusion: { in: [*grade_levels.keys, nil] }
  validates :badge_color, inclusion: { in: [*badge_colors.keys, nil] }
  validates :user_id, :role_id, numericality: { only_integer: true }, presence: true, allow_blank: false
  validates :purview_circle_id, :parent_circle_id, numericality: { only_integer: true }, allow_nil: true
  validates :primary_role, inclusion: [true, false]
  validates :start_date, presence: true, allow_blank: false

  validate :validate_parent_circle_id, :validate_parent_circle_level,
           :validate_purview_circle_level, :validate_purview_circle_id, :validate_primary_role,
           :validate_user_id_and_role_id, :check_for_badge_ring_and_color
  validate :validate_file_or_proof_link, if: -> { verification_status == 'verified' }
  validate :validate_is_self_claimed_or_is_letter_pending, if: -> { verification_status == 'unverified' }
  validate :check_for_quota_value_limit, on: :create
  validate :check_date_value,  if: -> { errors.blank? }
  validate :check_for_quota_limit_on_update, on: :update
  validate :start_date_being_less_than_end_date, if: -> { errors.blank? }
  validate :validate_uniqueness_of_user_role, if: -> { errors.blank? }
  validate :check_for_show_on_about_page_count, if: -> { errors.blank? }
  validate :validate_badge_icon_id, if: -> { errors.blank? }

  after_commit :update_other_primary_role, :index_for_search, :log_badge_addition_or_updation, :flush_user_cache,
               :update_invite_card, :join_relevant_circles, :re_index_user_invites_after_user_badge_update,
               :frame_recommendations, :revaluate_sub_circles_of_user, :trigger_identity_image_generation
  after_commit :send_notifications, :trigger_transliteration_of_badge_description, :update_floww_lead_score,
               :update_floww_lead_score_of_user_contacts
  after_create_commit :send_to_mixpanel
  after_destroy :log_badge_removal
  before_save :update_primary_role_if_inactive, :update_marketing_consent_of_user, :check_overridable_data
  before_save :change_end_date_if_nil, if: -> { end_date.blank? }
  validate :check_user_had_primary_role, if: -> { primary_role == false }

  transliteration name_attr: :get_description, force_update_on_commit: true

  def send_to_mixpanel
    if active? && primary_role?
      EventTracker.perform_async(user_id,
                                 'assigned_badge_backend',
                                 {
                                   'badge_text' => get_description,
                                   'grade_level' => get_grade_level,
                                   'role_id' => role_id,
                                 })
    end
  end

  def check_date_value
    max_date = UserRole::DEFAULT_USER_ROLE_END_DATE
    if end_date.present? && end_date > max_date
      errors.add(:end_date, "end_date should not be greater than maximum date i.e., #{max_date}")
    end
  end

  # Override for reindex happening inside transliteration
  def reindex(method_name = nil, mode: nil, refresh: false)
    user.reindex(method_name, mode: mode, refresh: refresh)
  end

  def trigger_transliteration_of_badge_description
    if saved_change_to_role_id? || saved_change_to_parent_circle_id? || saved_change_to_purview_circle_id? || saved_change_to_primary_role? || saved_change_to_badge_icon_id? || saved_change_to_free_text? || saved_change_to_display_name_order? || saved_change_to_active?
      TransliterateEntityName.perform_async(self.class.name, self.id)
    end
  end

  enum verification_status: {
    unverified: 'unverified',
    verified: 'verified',
  }, _suffix: true

  aasm column: :verification_status, enum: true do
    state :unverified, initial: true
    state :verified

    event :mark_as_verified do
      transitions from: :unverified, to: :verified
    end
  end

  def validate_parent_circle_id
    if role.present? && role.parent_circle_id.present? && parent_circle_id.present?
      errors.add(:parent_circle_id, "Given #{role.name} role has parent circle id, so parent circle id not required")
    elsif role.present? && role.parent_circle_id.blank? && parent_circle_id.blank?
      errors.add(:parent_circle_id, "Given #{role.name} role has no parent circle id, so parent circle id required")
    elsif role.present? && parent_circle_id.present? && parent_circle.blank?
      errors.add(:parent_circle_id, "Circle must exist")
    end
  end

  def validate_file_or_proof_link
    if file.blank? && proof_link.blank? && active == true
      errors.add(:file, 'Either a file or proof link must be present for a verified user.')
    end
  end

  def validate_is_self_claimed_or_is_letter_pending
    if !is_self_claimed && !is_letter_pending && active == true
      errors.add(:is_letter_pending, 'Either a is_self_claimed or is_letter_pending must be present for a unverified user.')
    end
  end

  def eligible_to_trigger_sub_circle_revaluation?
    (role.present? && role.parent_circle.present? && role.parent_circle.eligibile_for_sub_circle_flow?) || (parent_circle.present? && parent_circle.eligibile_for_sub_circle_flow?)
  end

  def revaluate_sub_circles_of_user
    if eligible_to_trigger_sub_circle_revaluation?
      UserSubCirclesRevaluation.perform_async(self.user_id)
    end
  end

  def self.ransackable_associations(auth_object = nil)
    ["parent_circle", "role", "purview_circle", "user"]
  end

  def validate_purview_circle_id
    return unless role.present?

    if role.has_purview
      if purview_circle_id.blank?
        errors.add(:purview_circle_id, "Purview circle ID is required for roles with purview")
      elsif purview_circle.blank?
        errors.add(:purview_circle_id, "Circle must exist")
      end
    elsif purview_circle_id.present?
      errors.add(:purview_circle_id, "Purview circle is not required for the selected role")
    end
  end

  def validate_user_id_and_role_id
    errors.add(:user_id, "User must exist") if user.blank?
    errors.add(:role_id, "Role must exist") if role.blank?
  end

  def validate_parent_circle_level
    if role.present? && role.parent_circle_level.present? && parent_circle.present? && role.parent_circle_level !=
      parent_circle.level
      errors.add(:parent_circle_id, 'parent_circle_level of the role is not matched with the given circle id level')
    end
  end

  def validate_purview_circle_level
    if role.present? && role.has_purview && purview_circle.present? && role.purview_level != purview_circle.level
      errors.add(:purview_circle_id, 'purview_circle_level of the role is not matched with the
                                                      given circle id level')
    end
  end

  def check_for_quota_value_limit
    if role.present? && role.absolute_quota_type?
      role_count = UserRole.where(parent_circle_id: parent_circle_id, role_id: role_id, active: true).where.not(id: self.id)
                           .count
      if role_count >= role.quota_value
        errors.add(:role_id, 'Users with this role limit has been reached in this circle')
      end

    elsif role.present? && role.percentage_quota_type?
      role_count = UserRole.where(parent_circle_id: parent_circle_id, role_id: role_id, active: true).count
      role_limit = (parent_circle.get_members_count * (role.quota_value / 100.0)).to_i
      if role_count >= role_limit
        errors.add(:role_id, I18n.t('errors.attributes.user_role.role_id.role_limit_reached'))
      end
    end
  end

  def check_for_quota_limit_on_update
    return if active_changed?(from: true, to: false) || badge_ring_changed? || badge_color_changed? ||
      grade_level_changed? || purview_circle_id_changed? || !self.changed? || !active
    self.check_for_quota_value_limit
  end

  def update_other_primary_role
    if primary_role
      UserRole.where(user_id: user_id, primary_role: true).where.not(id: self.id).update_all(primary_role: false)
    end
  end

  def validate_primary_role
    if role.present? && !role.has_badge && primary_role && active
      errors.add(:primary_role, "primary role should be false for the the selected role")
    end
  end

  def update_primary_role_if_inactive
    self.primary_role = false if !active && primary_role
  end

  def check_user_had_primary_role
    if role.present? && role.has_badge && !primary_role && active
      has_primary_role = UserRole.where(user_id: user_id, primary_role: true, active: true).exists?
      unless has_primary_role
        errors.add(:primary_role, "User don't have any other primary role, make this as a primary role")
      end
    end
  end

  def check_for_badge_ring_and_color
    if role.present? && !role.has_badge
      errors.add(:badge_ring, "badge ring should be empty if role don't have badge") unless badge_ring.blank?
      errors.add(:badge_color, "badge color should be empty if role don't have badge") unless badge_color.blank?
    end
  end

  def validate_uniqueness_of_user_role
    if self.new_record? || user_id_changed? || role_id_changed? || start_date_changed? || end_date_changed?
      end_date = self.end_date.blank? ? UserRole::DEFAULT_USER_ROLE_END_DATE : self.end_date
      user_role_exists = UserRole.where(user_id: self.user_id, role_id: self.role_id)
                                 .where.not(id: self.id)
                                 .where("start_date between ? and ? or end_date between ? and ? or (start_date <= ? and ? <= end_date)",
                                        self.start_date, end_date,
                                        self.start_date, end_date,
                                        self.start_date, end_date).exists?
      if user_role_exists
        errors.add(:user_id, "user with this role and duration already exists, can't create duplicate")
      end
    end
  end

  def validate_badge_icon_id
    if self.role.present?
      if self.role.has_badge && self.badge_icon_id.present?
        badge_icon = BadgeIcon.find_by(id: self.badge_icon_id)
        errors.add(:badge_icon_id, "badge icon must exist") if badge_icon.blank?
      elsif !self.role.has_badge && self.badge_icon_id.present?
        errors.add(:badge_icon_id, "badge icon should not exist")
      end
    end
  end

  def check_for_show_on_about_page_count
    if self.show_on_about_page
      if UserRole.where(user_id: self.user_id, show_on_about_page: true).count > 5
        errors.add(:show_on_about_page, "only 5 roles to show on about page")
      end
    end
  end

  def start_date_being_less_than_end_date
    end_date = self.end_date.present? ? self.end_date : UserRole::DEFAULT_USER_ROLE_END_DATE
    if self.start_date > end_date
      errors.add(:start_date, "User role duration is not valid, start date is ahead of end date")
    end
  end

  def change_end_date_if_nil
    self.end_date = UserRole::DEFAULT_USER_ROLE_END_DATE
  end

  def get_badge_icon_url
    badge_icon = nil
    badge_icon = BadgeIcon.find_by_id(badge_icon_id).admin_medium.url if badge_icon_id.present?
    # add quality filter 80 and resize to 100x100 for badge icon
    badge_icon = badge_icon.gsub(Photo::PHOTO_REGEX, 'https://a-cdn.thecircleapp.in/fit-in/100x100/filters:quality(80)/') if badge_icon.present?
    badge_icon || Constants.get_transparent_badge_icon_url
  end

  def get_badge_ring
    get_badge_ring = badge_ring.nil? ? role.badge_ring : badge_ring
    badge_color = get_badge_color
    case true
    when get_badge_ring && badge_color == "GOLD"
      "GOLD_RING"
    when get_badge_ring && badge_color == "SILVER"
      "SILVER_RING"
    else
      "NO_RING"
    end
  end

  def get_badge_color
    self.badge_color || self.role.badge_color
  end

  def get_json
    description = ""
    badge_color = "NONE"
    if role.show_badge_banner
      badge_color = self.get_badge_color
      description = self.get_description
    end

    {
      "id" => self.id,
      "active" => self.active,
      "icon_url" => self.get_badge_icon_url,
      "badge_banner" => badge_color,
      "badge_icon" => self.get_badge_icon_url,
      "badge_ring" => self.get_badge_ring,
      "badge_text" => self.get_badge_text_name,
      "description" => description,
      "is_celebrated" => self.is_celebrated,
      "grade_level" => self.get_readable_grade_level,
      "role_id" => self.role_id,
      "verification_status" => self.verification_status,
    }
  end

  def get_badge_text_name
    self.get_role_name
  end

  # as we have removed ex-roles so we need to modify the name of role
  def get_role_name
    text = role.name
    end_date = self.end_date.present? ? self.end_date : UserRole::DEFAULT_USER_ROLE_END_DATE
    # if role is ex-role then add Ex- or మాజీ before the role name
    if Date.parse(Time.zone.today.to_s) > end_date
      # if text is starting with alphabet then add Ex- else add మాజీ
      text = text.match?(/\A[a-zA-Z]/) ? "Ex-" + text : "మాజీ " + text
    end
    text
  end

  def get_badge_icon_color(badge_color, badge_icon_ribbon)
    case true
    when badge_color == "GOLD" && badge_icon_ribbon
      "GOLD"
    when badge_color == "SILVER" && badge_icon_ribbon
      "SILVER"
    when (badge_color == "GOLD" && !badge_icon_ribbon) || (badge_color == "SILVER" && !badge_icon_ribbon) ||
      (badge_color == "WHITE")
      "WHITE"
    else
      nil
    end
  end

  def get_description
    description = ""

    if self.role.has_free_text && self.free_text.present?
      return self.free_text
    end

    display_name_order = self.display_name_order.present? ? self.display_name_order : self.role.display_name_order
    if display_name_order.present?
      identifier_order = display_name_order.split(",")
      identifier_order.each do |identifier|
        case identifier
        when "parent"
          parent_circle = self.parent_circle.present? ? self.parent_circle : self.role.parent_circle
          description += " #{parent_circle.get_circle_name_for_description}" if parent_circle.present?
        when "role"
          description += " #{self.get_role_name}"
        when "purview"
          description += " #{purview_circle.get_circle_name_for_description}" if purview_circle.present?
        when "purview_suffix"
          description += " #{purview_circle.level_verbose_for_role_purview}" if purview_circle.present?
        when "badge_text"
          badge_icon_circle_id = self.get_badge_icon_circle_id
          if badge_icon_circle_id.present? && badge_icon_circle_id != 0
            description += " #{Circle.find(badge_icon_circle_id)&.get_circle_name_for_description}"
          end
        end
      end
    end
    description.strip
  end

  def get_badge_icon_circle_id
    circle_id = 0
    if self.badge_icon_id.present?
      badge_icon_circle_id = BadgeIcon.find(self.badge_icon_id).badge_icon_group.circle_id
      circle_id = badge_icon_circle_id.present? && Circle.find(badge_icon_circle_id).political_party_level? ? badge_icon_circle_id : 0
    end
    circle_id
  end

  def get_badge_user_affiliated_party_circle_id
    circle_id = 0
    if self.role.political_party_parent_circle_level?
      circle_id = self.parent_circle_id
    elsif self.badge_icon_id.present?
      badge_icon_circle_id = BadgeIcon.find(self.badge_icon_id).badge_icon_group.circle_id

      circle_id = badge_icon_circle_id.present? && Circle.find(badge_icon_circle_id).political_party_level? ? badge_icon_circle_id : 0

    end
    circle_id
  end

  def get_badge_user_affiliated_location_circle_id
    circle_id = 0
    if parent_circle&.location_circle_type? || role.parent_circle&.location_circle_type?
      circle_id = parent_circle_id
    elsif purview_circle&.location_circle_type?
      circle_id = purview_circle_id
    end
    circle_id
  end

  def get_readable_grade_level
    if self.grade_level.present?
      grade_level = self.grade_level
    else
      grade_level = self.role.grade_level
    end
    UserRole.grade_levels[grade_level]
  end

  def get_grade_level
    if self.grade_level.present?
      grade_level = self.grade_level
    else
      grade_level = self.role.grade_level
    end
    grade_level
  end

  def get_badge_role_header_for_badge_celebration
    header = self.get_role_name
    header.tr("_", " ")
  end

  def get_sub_header_for_badge_celebration
    ""
  end

  def index_for_search
    user.index_for_search(true)
  end

  def log_badge_addition_or_updation
    self.user.send_to_mixpanel
  end

  def flush_user_cache
    user.flush_cache
  end

  def re_index_user_invites_after_user_badge_update
    UpdateUserInvitesIndex.perform_async(user_id)
  end

  def update_marketing_consent_of_user
    user.update(marketing_consent: @marketing_consent) unless @marketing_consent.nil?
  end

  def check_overridable_data
    if badge_color.present? && badge_color == role.badge_color
      self.badge_color = nil
    end

    unless badge_ring.nil? || badge_ring != role.badge_ring
      self.badge_ring = nil
    end

    if grade_level.present? && grade_level == role.grade_level
      self.grade_level = nil
    end
  end

  def can_notify_user?
    active? && verified_verification_status? && primary_role? && !is_celebrated? && role.has_badge?
  end

  def send_notifications
    SendUserRoleNotifications.perform_async(id) if (self.saved_change_to_active? || self.saved_change_to_verification_status?) && self.can_notify_user?
  end

  def send_notification_for_badge_assigned_user
    if role_id == 37 || role_id == 35 # Top Fan or Journalist
      notification_title = "'Praja' వేల సభ్యుల్లో మీరు ఒక ప్రసిద్ధులు"
      notification_body = "అందుకే మీకు \"#{get_description}\" గా 'Praja' ప్రత్యేక రివార్డు."
    else
      notification_title = "🥳 \"#{get_description}\" గా మీకు ప్రత్యేక గుర్తింపు !!"
      notification_body = "ఇప్పుడు మీకు Praja App లో స్పెషల్ బ్యాడ్జి"
    end

    notification = Notification.create!(description: notification_title,
                                        notification_type: :user_badge,
                                        user_id: user_id,
                                        entity_type: "UserBadge",
                                        entity_id: id)

    payload = {
      "title": notification_title,
      "body": notification_body,
      "message_channel": "important",
      "data": {
        "path": "/badge-notification",
        "circle_notification_id": notification.id.to_s
      }
    }

    GarudaNotification.send_user_notification(user.id, payload)
  end

  def check_grade_level_for_internal_notification
    [:grade_1, :grade_2, :grade_3, :grade_4].include? self.get_grade_level.to_sym
  end

  # TODO :: Need to confirm with product team about the notification logic after roles restructuring
  def check_to_send_internal_notification
    check_grade_level_for_internal_notification && (parent_circle&.location_circle_type? || role.parent_circle&.location_circle_type?)
  end

  def send_internal_notification
    return unless check_to_send_internal_notification

    user_ids = case true
               when parent_circle.is_village_level?
                 User.where(village_id: parent_circle_id || role.parent_circle_id).ids

               when parent_circle.mandal_level?
                 User.where(mandal_id: parent_circle_id || role.parent_circle_id).ids

               when parent_circle.mla_constituency_level?
                 User.where(mla_constituency_id: parent_circle_id || role.parent_circle_id).ids

               when parent_circle.district_level?
                 User.where(district_id: parent_circle_id || role.parent_circle_id).ids

               when parent_circle.mp_constituency_level?
                 User.where(mp_constituency_id: parent_circle_id || role.parent_circle_id).ids
               else
                 []
               end
    batch_users_ids(user_ids)

  end

  def batch_users_ids(user_ids)
    uids = user_ids.filter_map { |u| u unless u == user_id }

    no_notification_ids = Notification.internal_notifications_count_set(uids)
    notification_user_ids = uids - no_notification_ids

    parent_circle_name = parent_circle&.name || role.parent_circle.name
    notification_title = "మీ #{parent_circle_name} #{get_role_name} అయిన #{user.name} గారు ఇప్పుడు యాప్ లో చేరారు"
    notification_message = "వెంటనే కనెక్ట్ అయ్యి మరిన్ని విశేషాలు తెలుసుకోండి"

    notification_user_ids.each do |user_id|
      payload = {
        "title": notification_title,
        "body": notification_message,
        "message_channel": "badge_user_join",
        "data": {
          "path": "/users/#{user_id}"
        }
      }
      GarudaNotification.send_user_notification(user_id, payload)
    end
  end

  def update_invite_card
    if saved_change_to_role_id? || saved_change_to_parent_circle_id? || saved_change_to_purview_circle_id? ||
      saved_change_to_badge_color? || saved_change_to_badge_ring? ||
      saved_change_to_primary_role?(from: false, to: true) || saved_change_to_badge_icon_id? || saved_change_to_free_text?
      GenerateInviteCard.perform_async(self.user_id)
    end
  end

  def log_badge_removal
    MixpanelIntegration.perform_async(user_id, { 'badge' => 'No', 'badge_role' => 'null', "grade" => 'null' })
  end

  def join_relevant_circles
    # if parent_circle_id then join the user to new political circle and un_join the old
    # political circle
    if saved_change_to_parent_circle_id? && self.role.political_party_parent_circle_level?
      un_join_circle(self.parent_circle_id_before_last_save) if self.parent_circle_id_before_last_save.present?
      join_circle(self.parent_circle_id)
    elsif saved_change_to_badge_icon_id?
      previous_badge_icon_affiliated_circle_id = nil
      previous_badge_icon_affiliated_circle_id = BadgeIcon.find(self.badge_icon_id_before_last_save).badge_icon_group
                                                          .circle_id if self.badge_icon_id_before_last_save.present?

      un_join_circle(previous_badge_icon_affiliated_circle_id) if previous_badge_icon_affiliated_circle_id.present? &&
        Circle.find(previous_badge_icon_affiliated_circle_id).political_party_level?
      if self.badge_icon_id.present?
        badge_icon_affiliated_circle_id = BadgeIcon.find(self.badge_icon_id).badge_icon_group.circle_id
        join_circle(badge_icon_affiliated_circle_id) if badge_icon_affiliated_circle_id.present? &&
          Circle.find(badge_icon_affiliated_circle_id).political_party_level?
      end
    end
    # for some cases like if user already joined in the circle or already unjoined in the circle then
    # affiliated party circle id will not be updated to handle this case we need to update the affiliated party circle
    # id for the user
    user.update_affiliated_party_circle_id
  end

  # un_join the user from circle if user is joined to the circle automatically
  def un_join_circle(circle_id)
    UserCircle.find_by(circle_id: circle_id, user_id: self.user_id, source_of_join: :auto)&.destroy
  end

  def join_circle(circle_id)
    begin
      circle = Circle.find(circle_id)
      unless UserCircle.where(circle_id: circle_id, user_id: user_id).exists?
        UserCircle.create(circle_id: circle_id, user_id: user_id, source_of_join: :auto)
        $mixpanel_tracker.track(user_id,
                                'join_circle',
                                { 'circle_id': circle_id, 'circle_name': circle.name, 'source': 'badge-addition' })
      end
      return true
    rescue
      return false
    end
  end

  def get_primary_circle
    if parent_circle&.governing_body_circle_type? || role.parent_circle&.governing_body_circle_type?
      purview_circle
    elsif parent_circle&.interest_circle_type? || role.parent_circle&.interest_circle_type? ||
      parent_circle&.business_circle_type? || role.parent_circle&.business_circle_type?
      parent_circle || role.parent_circle
    else
      purview_circle
    end
  end

  def badge_description_type
    badge_text_length = self.get_description.length
    if badge_text_length > 15
      "long"
    else
      "short"
    end
  end

  def frame_recommendations
    # if name is changed, then we need to update frame recommendations for the user who has user poster layout data
    # and not subscribed to posters yet
    if (saved_change_to_role_id? || saved_change_to_parent_circle_id? || saved_change_to_purview_circle_id? ||
      saved_change_to_primary_role?(from: false, to: true) || saved_change_to_badge_icon_id? ||
      saved_change_to_free_text?) && UserPosterLayout.where(entity: self.user).exists? &&
      !self.user.is_poster_subscribed
      # first delete the existing frame recommendations
      self.user.delete_frame_recommendations
      # create new frame recommendations
      self.user.create_frame_recommendations
    end
  end

  def trigger_identity_image_generation
    # Trigger identity image generation when badge description-related attributes change
    if saved_change_to_role_id? || saved_change_to_parent_circle_id? || saved_change_to_purview_circle_id? ||
       saved_change_to_primary_role? || saved_change_to_badge_icon_id? || saved_change_to_free_text? ||
       saved_change_to_active?
      IdentityPhotoGenerationWorker.perform_async(user_id)
    end
  end

  def update_floww_lead_score
    # if changes done on active or a new record then we need to update the lead score
    UpdateLeadScore.perform_async(user_id) if saved_change_to_active? || saved_change_to_id? || saved_change_to_verification_status?
  end

  def update_floww_lead_score_of_user_contacts
    # if changes done on active or a new record then we need to update the lead score of contacts
    UpdateLeadScoreOfContacts.perform_async(user_id) if saved_change_to_active? || saved_change_to_id? || saved_change_to_verification_status?
  end
end
